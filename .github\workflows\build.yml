name: Build Spotify2MP3

permissions:
  contents: write

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to create'
        default: '1.3.2'
        required: true
        type: string

env:
  LEVEL: ${{ inputs.version }}

jobs:
  build-windows:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt pyinstaller

    - name: Download and setup ffmpeg
      run: |
        mkdir ffmpeg
        cd ffmpeg
        Invoke-WebRequest -Uri "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip" -OutFile "ffmpeg.zip"
        Expand-Archive -Path "ffmpeg.zip" -DestinationPath "."
        Move-Item -Path "ffmpeg-master-latest-win64-gpl\bin\ffmpeg.exe" -Destination "."
        Remove-Item -Recurse -Force "ffmpeg-master-latest-win64-gpl"
        Remove-Item "ffmpeg.zip"

    - name: Download and setup yt-dlp
      run: |
        mkdir yt-dlp
        cd yt-dlp
        Invoke-WebRequest -Uri "https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe" -OutFile "yt-dlp.exe"

    - name: Build with PyInstaller
      run: |
        pyinstaller Spotify2MP3-Windows.spec
        Rename-Item -Path "dist/Spotify2MP3.exe" -NewName "Spotify2MP3_Windows.exe"

    - name: Create GitHub Draft
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v${{ env.LEVEL }}
        name: Spotify2MP3 v${{ env.LEVEL }}
        draft: true
        prerelease: false
        files: dist/Spotify2MP3_Windows.exe
        
  build-macos:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt pyinstaller 

    - name: Download and setup ffmpeg
      run: |
        mkdir -p ffmpeg
        cd ffmpeg
        curl -L https://evermeet.cx/ffmpeg/ffmpeg-7.1.1.7z -o ffmpeg.7z
        tar -xvf ffmpeg.7z
        rm -rf ffmpeg.7z

    - name: Download and setup yt-dlp
      run: |
        mkdir -p yt-dlp
        cd yt-dlp
        curl -L https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp_macos -o yt-dlp
        chmod +x yt-dlp
        
    - name: Build with PyInstaller and create zip file
      run: |
        pyinstaller Spotify2MP3-macOS.spec
        cd dist
        ditto -c -k --keepParent Spotify2MP3.app Spotify2MP3_macOS.zip

    - name: Create GitHub Draft
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v${{ env.LEVEL }}
        name: Spotify2MP3 v${{ env.LEVEL }}
        draft: true
        prerelease: false
        files: dist/Spotify2MP3_macOS.zip

  build-linux-x64:
    runs-on: ubuntu-22.04
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.13'
        
    - name: Install dependencies
      run: |
        python -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt pyinstaller

    - name: Download and setup ffmpeg and yt-dlp
      run: |
        mkdir -p ffmpeg
        cd ffmpeg
        wget -O ffmpeg.tar.xz https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz
        tar -xvf ffmpeg.tar.xz
        cd ffmpeg-*-amd64-static
        mv ffmpeg ../
        cd ..
        rm -rf ffmpeg-*-amd64-static
        rm -rf ffmpeg.tar.xz
        cd ..
        mkdir -p yt-dlp
        cd yt-dlp
        wget -O yt-dlp https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp_linux
        chmod +x yt-dlp
        
    - name: Build with PyInstaller and create AppImage
      run: |
        source venv/bin/activate
        pyinstaller Spotify2MP3-Linux.spec
        chmod +x dist/Spotify2MP3/Spotify2MP3
        mkdir -p AppDir/usr/bin
        cp -r dist/Spotify2MP3 AppDir/Spotify2MP3
        cp ffmpeg/ffmpeg AppDir/usr/bin/ffmpeg
        cp yt-dlp/yt-dlp AppDir/usr/bin/yt-dlp
        cp config.json AppDir/config.json
        cp icon.png AppDir/icon.png
        echo '[Desktop Entry]' > AppDir/Spotify2MP3.desktop
        echo 'Type=Application' >> AppDir/Spotify2MP3.desktop
        echo 'Name=Spotify2MP3' >> AppDir/Spotify2MP3.desktop
        echo 'Exec=Spotify2MP3/Spotify2MP3' >> AppDir/Spotify2MP3.desktop
        echo 'Icon=icon' >> AppDir/Spotify2MP3.desktop
        echo 'Categories=AudioVideo;Audio;Music;' >> AppDir/Spotify2MP3.desktop
        echo '#!/bin/sh' > AppDir/AppRun
        echo 'APPDIR=$(dirname "$(readlink -f "${0}")")' >> AppDir/AppRun
        echo 'exec "${APPDIR}/Spotify2MP3/Spotify2MP3" "$@"' >> AppDir/AppRun
        chmod +x AppDir/AppRun
        wget -c "https://github.com/AppImage/appimagetool/releases/download/continuous/appimagetool-x86_64.AppImage" -O appimagetool.AppImage
        chmod +x appimagetool.AppImage
        ./appimagetool.AppImage AppDir Spotify2MP3_Linux_x86-64.AppImage
        chmod +x Spotify2MP3_Linux_x86-64.AppImage

    - name: Create GitHub Draft
      uses: softprops/action-gh-release@v1
      with:
        tag_name: v${{ env.LEVEL }}
        name: Spotify2MP3 v${{ env.LEVEL }}
        draft: true
        prerelease: false
        files: Spotify2MP3_Linux_x86-64.AppImage